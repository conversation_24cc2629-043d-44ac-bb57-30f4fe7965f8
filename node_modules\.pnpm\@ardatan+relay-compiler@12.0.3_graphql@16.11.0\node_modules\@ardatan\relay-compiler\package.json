{"name": "@ardatan/relay-compiler", "description": "A compiler tool for building GraphQL-driven applications.", "version": "12.0.3", "keywords": ["graphql", "relay"], "license": "MIT", "homepage": "https://relay.dev", "bugs": "https://github.com/facebook/relay/issues", "repository": "facebook/relay", "main": "index.js", "bin": {"relay-compiler": "bin/relay-compiler"}, "dependencies": {"@babel/generator": "^7.26.10", "@babel/parser": "^7.26.10", "@babel/runtime": "^7.26.10", "chalk": "^4.0.0", "fb-watchman": "^2.0.0", "immutable": "~3.7.6", "invariant": "^2.2.4", "nullthrows": "^1.1.1", "relay-runtime": "12.0.0", "signedsource": "^1.0.0"}, "peerDependencies": {"graphql": "*"}, "publishConfig": {"access": "public"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}